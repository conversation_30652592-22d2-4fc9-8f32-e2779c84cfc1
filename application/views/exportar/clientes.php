<?php header('Content-type: text/xml charset=utf-8', true); ?>
<?="<?xml version=\"1.0\" encoding=\"UTF-8\"?>" ?>

<DataHora><?= $data_hora ?></DataHora>
<Pagina><?= isset($_GET['pg']) ? $_GET['pg'] : 1 ?></Pagina>
<TotalPaginas><?= $total_paginas ?></TotalPaginas>
<Clientes>
	<?php foreach ($clientes as $cliente) { ?>
		<Cliente>
			<ID><?= isset($cliente['id']) ? $cliente['id'] : '' ?></ID>
			<Nome><![CDATA[<?= isset($cliente['nome']) ? $cliente['nome'] : '' ?>]]></Nome>
			<CPF><?= isset($cliente['cpf']) ? $cliente['cpf'] : '' ?></CPF>
			<RG><?= isset($cliente['rg']) ? $cliente['rg'] : '' ?></RG>
			<DataNascimento><?= isset($cliente['nascimento']) ? $cliente['nascimento'] : '' ?></DataNascimento>
			<Email><?= isset($cliente['email']) ? $cliente['email'] : '' ?></Email>
			<Codigo><?= isset($cliente['codigo']) ? $cliente['codigo'] : '' ?></Codigo>
			<Contatos>
				<Telefone1>
					<DDD><?= isset($cliente['ddd1']) ? $cliente['ddd1'] : '' ?></DDD>
					<Numero><?= isset($cliente['telefone1']) ? $cliente['telefone1'] : '' ?></Numero>
					<Tipo><?= isset($cliente['tipo_telefone1']) ? $cliente['tipo_telefone1'] : '' ?></Tipo>
				</Telefone1>
				<Telefone2>
					<DDD><?= isset($cliente['ddd2']) ? $cliente['ddd2'] : '' ?></DDD>
					<Numero><?= isset($cliente['telefone2']) ? $cliente['telefone2'] : '' ?></Numero>
					<Tipo><?= isset($cliente['tipo_telefone2']) ? $cliente['tipo_telefone2'] : '' ?></Tipo>
				</Telefone2>
				<Telefone3>
					<DDD><?= isset($cliente['ddd3']) ? $cliente['ddd3'] : '' ?></DDD>
					<Numero><?= isset($cliente['telefone3']) ? $cliente['telefone3'] : '' ?></Numero>
					<Tipo><?= isset($cliente['tipo_telefone3']) ? $cliente['tipo_telefone3'] : '' ?></Tipo>
				</Telefone3>
			</Contatos>
			<Conjuge>
				<Nome><![CDATA[<?= isset($cliente['conjuge_nome']) ? $cliente['conjuge_nome'] : '' ?>]]></Nome>
				<DataNascimento><?= isset($cliente['conjuge_nascimento']) ? $cliente['conjuge_nascimento'] : '' ?></DataNascimento>
				<Email><?= isset($cliente['conjuge_email']) ? $cliente['conjuge_email'] : '' ?></Email>
				<Telefone>
					<DDD><?= isset($cliente['conjuge_ddd']) ? $cliente['conjuge_ddd'] : '' ?></DDD>
					<Numero><?= isset($cliente['conjuge_telefone']) ? $cliente['conjuge_telefone'] : '' ?></Numero>
				</Telefone>
			</Conjuge>
			<Endereco>
				<Logradouro><![CDATA[<?= isset($cliente['logradouro']) ? $cliente['logradouro'] : '' ?>]]></Logradouro>
				<Endereco><![CDATA[<?= isset($cliente['endereco']) ? $cliente['endereco'] : '' ?>]]></Endereco>
				<Numero><?= isset($cliente['numero']) ? $cliente['numero'] : '' ?></Numero>
				<Complemento><![CDATA[<?= isset($cliente['complemento']) ? $cliente['complemento'] : '' ?>]]></Complemento>
				<CEP><?= isset($cliente['cep']) ? $cliente['cep'] : '' ?></CEP>
				<Bairro><![CDATA[<?= isset($cliente['bairro']) ? $cliente['bairro'] : '' ?>]]></Bairro>
				<Cidade><![CDATA[<?= isset($cliente['cidade']) ? $cliente['cidade'] : '' ?>]]></Cidade>
				<UF><?= isset($cliente['uf']) ? $cliente['uf'] : '' ?></UF>
			</Endereco>
			<Informacoes>
				<Categoria><?= isset($cliente['categoria']) ? $cliente['categoria'] : '' ?></Categoria>
				<Status><?= isset($cliente['status']) ? $cliente['status'] : '' ?></Status>
				<RendaPessoal><?= isset($cliente['renda_pessoal']) ? $cliente['renda_pessoal'] : '' ?></RendaPessoal>
				<RendaFamilia><?= isset($cliente['renda_familia']) ? $cliente['renda_familia'] : '' ?></RendaFamilia>
				<FGTS><?= isset($cliente['fgts']) ? $cliente['fgts'] : '' ?></FGTS>
				<Sinal><?= isset($cliente['sinal']) ? $cliente['sinal'] : '' ?></Sinal>
				<ProcurandoDesde><?= isset($cliente['procurando_desde']) ? $cliente['procurando_desde'] : '' ?></ProcurandoDesde>
				<OfertaAtiva><?= isset($cliente['oferta_ativa']) ? $cliente['oferta_ativa'] : '' ?></OfertaAtiva>
				<ListaOfertaAtiva><?= isset($cliente['lista_oferta_ativa']) ? $cliente['lista_oferta_ativa'] : '' ?></ListaOfertaAtiva>
			</Informacoes>
			<Corretor>
				<ID><?= isset($cliente['corretor_id']) ? $cliente['corretor_id'] : '' ?></ID>
				<Nome><![CDATA[<?= isset($cliente['corretor']) ? $cliente['corretor'] : '' ?>]]></Nome>
			</Corretor>
			<Datas>
				<Inclusao><?= isset($cliente['inclusao']) ? $cliente['inclusao'] : '' ?></Inclusao>
				<Alteracao><?= isset($cliente['alteracao']) ? $cliente['alteracao'] : '' ?></Alteracao>
			</Datas>
			<Observacoes><![CDATA[<?= isset($cliente['texto']) ? $cliente['texto'] : '' ?>]]></Observacoes>
			<Atendimentos>
				<?php if (isset($cliente['atendimentos']) && is_array($cliente['atendimentos'])) { ?>
					<?php foreach ($cliente['atendimentos'] as $atendimento) { ?>
						<Atendimento>
							<ID><?= isset($atendimento['id']) ? $atendimento['id'] : '' ?></ID>
							<Transacao><?= isset($atendimento['transacao']) ? $atendimento['transacao'] : '' ?></Transacao>
							<ValorInicial><?= isset($atendimento['valor_inicial']) ? $atendimento['valor_inicial'] : '' ?></ValorInicial>
							<ValorFinal><?= isset($atendimento['valor_final']) ? $atendimento['valor_final'] : '' ?></ValorFinal>
							<DormitorioInicial><?= isset($atendimento['dormitorio_inicial']) ? $atendimento['dormitorio_inicial'] : '' ?></DormitorioInicial>
							<DormitorioFinal><?= isset($atendimento['dormitorio_final']) ? $atendimento['dormitorio_final'] : '' ?></DormitorioFinal>
							<SuitesInicial><?= isset($atendimento['suites_inicial']) ? $atendimento['suites_inicial'] : '' ?></SuitesInicial>
							<SuitesFinal><?= isset($atendimento['suites_final']) ? $atendimento['suites_final'] : '' ?></SuitesFinal>
							<AreaTotalInicial><?= isset($atendimento['area_total_inicial']) ? $atendimento['area_total_inicial'] : '' ?></AreaTotalInicial>
							<AreaTotalFinal><?= isset($atendimento['area_total_final']) ? $atendimento['area_total_final'] : '' ?></AreaTotalFinal>
							<AreaUtilInicial><?= isset($atendimento['area_util_inicial']) ? $atendimento['area_util_inicial'] : '' ?></AreaUtilInicial>
							<AreaUtilFinal><?= isset($atendimento['area_util_final']) ? $atendimento['area_util_final'] : '' ?></AreaUtilFinal>
							<GaragemInicial><?= isset($atendimento['garagem_inicial']) ? $atendimento['garagem_inicial'] : '' ?></GaragemInicial>
							<GaragemFinal><?= isset($atendimento['garagem_final']) ? $atendimento['garagem_final'] : '' ?></GaragemFinal>
							<CondominioInicial><?= isset($atendimento['condominio_inicial']) ? $atendimento['condominio_inicial'] : '' ?></CondominioInicial>
							<CondominioFinal><?= isset($atendimento['condominio_final']) ? $atendimento['condominio_final'] : '' ?></CondominioFinal>
							<Referencias><?= isset($atendimento['referencias']) ? $atendimento['referencias'] : '' ?></Referencias>
							<Tipos><![CDATA[<?= isset($atendimento['tipos']) ? $atendimento['tipos'] : '' ?>]]></Tipos>
							<Bairros><![CDATA[<?= isset($atendimento['bairros']) ? $atendimento['bairros'] : '' ?>]]></Bairros>
							<Observacoes><![CDATA[<?= isset($atendimento['observacoes']) ? $atendimento['observacoes'] : '' ?>]]></Observacoes>
							<Inclusao><?= isset($atendimento['inclusao']) ? $atendimento['inclusao'] : '' ?></Inclusao>
							<Alteracao><?= isset($atendimento['alteracao']) ? $atendimento['alteracao'] : '' ?></Alteracao>
							<Ativo><?= isset($atendimento['ativo']) ? $atendimento['ativo'] : '' ?></Ativo>
						</Atendimento>
					<?php } ?>
				<?php } ?>
			</Atendimentos>
			<Agendamentos>
				<?php if (isset($cliente['agendamentos']) && is_array($cliente['agendamentos'])) { ?>
					<?php foreach ($cliente['agendamentos'] as $agendamento) { ?>
						<Agendamento>
							<ID><?= isset($agendamento['id']) ? $agendamento['id'] : '' ?></ID>
							<Data><?= isset($agendamento['data']) ? $agendamento['data'] : '' ?></Data>
							<Hora><?= isset($agendamento['hora']) ? $agendamento['hora'] : '' ?></Hora>
							<Texto><![CDATA[<?= isset($agendamento['texto']) ? $agendamento['texto'] : '' ?>]]></Texto>
							<Status><?= isset($agendamento['status']) ? $agendamento['status'] : '' ?></Status>
							<Corretor><![CDATA[<?= isset($agendamento['nome']) ? $agendamento['nome'] : '' ?>]]></Corretor>
							<Responsavel><![CDATA[<?= isset($agendamento['responsavel']) ? $agendamento['responsavel'] : '' ?>]]></Responsavel>
							<FunilSequencial><?= isset($agendamento['funil_sequencial']) ? $agendamento['funil_sequencial'] : '' ?></FunilSequencial>
							<Inclusao><?= isset($agendamento['inclusao']) ? $agendamento['inclusao'] : '' ?></Inclusao>
						</Agendamento>
					<?php } ?>
				<?php } ?>
			</Agendamentos>
			<Recontatos>
				<?php if (isset($cliente['recontatos']) && is_array($cliente['recontatos'])) { ?>
					<?php foreach ($cliente['recontatos'] as $recontato) { ?>
						<Recontato>
							<Data><?= isset($recontato['data']) ? $recontato['data'] : '' ?></Data>
							<Texto><![CDATA[<?= isset($recontato['texto']) ? $recontato['texto'] : '' ?>]]></Texto>
							<Corretor><![CDATA[<?= isset($recontato['nome']) ? $recontato['nome'] : '' ?>]]></Corretor>
							<FunilSequencial><?= isset($recontato['funil_sequencial']) ? $recontato['funil_sequencial'] : '' ?></FunilSequencial>
						</Recontato>
					<?php } ?>
				<?php } ?>
			</Recontatos>
		</Cliente>
	<?php } ?>
</Clientes>
